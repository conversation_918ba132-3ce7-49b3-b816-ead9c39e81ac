import { <PERSON>, <PERSON>, Menu } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";

export function DarkHeader() {
  return (
    <div className="bg-slate-900/95 backdrop-blur-sm border-b border-slate-700 p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
            <Menu className="w-5 h-5" />
          </Button>
          <div>
            <h1 className="text-white text-xl">RepairPro Manager</h1>
            <p className="text-slate-400 text-sm">Daily Dashboard</p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white relative">
            <Bell className="w-5 h-5" />
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-[#00ff88] rounded-full"></div>
          </Button>
          <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
            <Search className="w-5 h-5" />
          </Button>
        </div>
      </div>
      
      <div className="text-slate-300 text-sm">
        Welcome back, <span className="text-[#00ff88]">Technician</span>
      </div>
    </div>
  );
}