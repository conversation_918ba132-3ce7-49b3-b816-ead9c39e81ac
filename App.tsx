import { <PERSON>Header } from "./components/DarkHeader";
import { StatsCard } from "./components/StatsCard";
import { JobCard } from "./components/JobCard";
import { DarkBottomNavigation } from "./components/DarkBottomNavigation";
import { LoadingAnimation } from "./components/LoadingAnimation";
import {
  Wrench,
  Package,
  CreditCard,
  Users,
  Smartphone,
  ScanLine,
} from "lucide-react";

export default function App() {
  const dailyStats = [
    {
      title: "Jobs Today",
      value: "5",
      total: "10",
      progress: 50,
      color: "#00ff88",
      icon: <Wrench className="w-4 h-4" />,
    },
    {
      title: "Parts Low in Stock",
      value: "5",
      progress: 23,
      color: "#fbbf24",
      icon: <Package className="w-4 h-4" />,
    },
    {
      title: "Pending Payments",
      value: "1",
      progress: 80,
      color: "#f97316",
      icon: <CreditCard className="w-4 h-4" />,
    },
    {
      title: "Technicians On Duty",
      value: "0",
      progress: 0,
      color: "#ef4444",
      icon: <Users className="w-4 h-4" />,
    },
  ];

  const repairJobs = [
    {
      deviceName: "SS S20/1",
      imei: "SS BB (E)",
      problem: "Screen replacement",
      status: "pending" as const,
      customer: "John Doe",
      assignedTo: "Mike",
    },
    {
      deviceName: "701 E29/20",
      imei: "SS R/00",
      problem: "Battery replacement",
      status: "in-progress" as const,
      customer: "Sarah Wilson",
      assignedTo: "Alex",
    },
    {
      deviceName: "SS4 308 390",
      imei: "SS R2 00",
      problem: "Camera repair",
      status: "in-progress" as const,
      customer: "Robert Brown",
      assignedTo: "Lisa",
    },
    {
      deviceName: "S HBB03 D118",
      imei: "SS5114O",
      problem: "Water damage repair",
      status: "in-progress" as const,
      customer: "Emily Davis",
      assignedTo: "Tom",
    },
    {
      deviceName: "1GB 794300",
      imei: "Device info",
      problem: "Charging port replacement",
      status: "completed" as const,
      customer: "David Miller",
      assignedTo: "Mike",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#1a1f2e] to-[#0f172a] text-white">
      {/* Header */}
      <DarkHeader />

      {/* Main Content */}
      <div className="pb-24">
        {/* Daily Statistics */}
        <div className="p-4">
          <div className="flex items-center gap-2 mb-4">
            <ScanLine className="w-5 h-5 text-[#00ff88]" />
            <h2 className="text-white">Daily Overview</h2>
          </div>
          <div className="grid grid-cols-2 gap-4">
            {dailyStats.map((stat, index) => (
              <StatsCard key={index} {...stat} />
            ))}
          </div>
        </div>

        {/* Repair Jobs Section */}
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Smartphone className="w-5 h-5 text-[#00ff88]" />
              <h2 className="text-white">Repair Jobs</h2>
            </div>
            <LoadingAnimation type="pulse" size="sm" />
          </div>

          <div className="space-y-3">
            {repairJobs.map((job, index) => (
              <JobCard key={index} {...job} />
            ))}
          </div>

          {/* Load More Button */}
          <div className="mt-6 flex justify-center">
            <button className="bg-[#00ff88]/20 hover:bg-[#00ff88]/30 border border-[#00ff88]/30 text-[#00ff88] px-6 py-3 rounded-full transition-colors flex items-center gap-2">
              <LoadingAnimation type="spin" size="sm" />
              Load More
            </button>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="p-4">
          <h3 className="text-slate-300 mb-3">Quick Actions</h3>
          <div className="flex gap-3 overflow-x-auto pb-2">
            <button className="bg-slate-800/50 hover:bg-slate-800/70 border border-slate-700 p-4 rounded-xl min-w-[120px] transition-colors">
              <div className="text-[#00ff88] mb-2">
                <Wrench className="w-6 h-6 mx-auto" />
              </div>
              <div className="text-sm text-slate-300">
                New Repair
              </div>
            </button>
            <button className="bg-slate-800/50 hover:bg-slate-800/70 border border-slate-700 p-4 rounded-xl min-w-[120px] transition-colors">
              <div className="text-[#00ff88] mb-2">
                <ScanLine className="w-6 h-6 mx-auto" />
              </div>
              <div className="text-sm text-slate-300">
                Scan IMEI
              </div>
            </button>
            <button className="bg-slate-800/50 hover:bg-slate-800/70 border border-slate-700 p-4 rounded-xl min-w-[120px] transition-colors">
              <div className="text-[#00ff88] mb-2">
                <Package className="w-6 h-6 mx-auto" />
              </div>
              <div className="text-sm text-slate-300">
                Inventory
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0">
        <DarkBottomNavigation />
      </div>
    </div>
  );
}