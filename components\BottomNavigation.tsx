import { Home, Wrench, Package, User } from "lucide-react";
import { useState } from "react";

interface NavItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
}

const navItems: NavItem[] = [
  { id: "home", label: "Home", icon: Home },
  { id: "jobs", label: "Jobs", icon: Wrench },
  { id: "inventory", label: "Inventory", icon: Package },
  { id: "profile", label: "Profile", icon: User },
];

export function BottomNavigation() {
  const [activeTab, setActiveTab] = useState("home");

  return (
    <div className="bg-[#0A223F] border-t border-slate-700">
      <div className="flex">
        {navItems.map((item) => {
          const IconComponent = item.icon;
          const isActive = activeTab === item.id;
          
          return (
            <button
              key={item.id}
              onClick={() => setActiveTab(item.id)}
              className={`flex-1 py-3 px-4 flex flex-col items-center gap-1 transition-colors ${
                isActive ? "text-[#00FFAA]" : "text-white/70 hover:text-white"
              }`}
            >
              <IconComponent className="w-6 h-6" />
              <span className="text-xs">{item.label}</span>
            </button>
          );
        })}
      </div>
    </div>
  );
}