export function TeamIllustration() {
  return (
    <div className="relative py-8 px-4">
      {/* Background bubbles */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-4 left-8 w-3 h-3 bg-[#FFA07A]/30 rounded-full"></div>
        <div className="absolute top-12 right-12 w-4 h-4 bg-[#FF6B6B]/20 rounded-full"></div>
        <div className="absolute bottom-8 left-12 w-2 h-2 bg-[#FFA07A]/40 rounded-full"></div>
        <div className="absolute bottom-12 right-8 w-3 h-3 bg-[#FF6B6B]/30 rounded-full"></div>
      </div>
      
      {/* Team illustration placeholder */}
      <div className="relative z-10 bg-white/50 rounded-3xl p-8 mx-4">
        <div className="flex flex-col items-center space-y-4">
          {/* Team members around table */}
          <div className="grid grid-cols-3 gap-4 mb-4">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="flex flex-col items-center">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                  i % 3 === 0 ? 'bg-[#FF6B6B]' : i % 2 === 0 ? 'bg-[#FFA07A]' : 'bg-gray-300'
                }`}>
                  <span className="text-white text-xs">👤</span>
                </div>
                <div className="mt-2 w-8 h-1 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
          
          {/* Table representation */}
          <div className="w-32 h-16 bg-gradient-to-r from-gray-200 to-gray-300 rounded-2xl relative">
            <div className="absolute top-2 left-4 w-6 h-4 bg-blue-400 rounded opacity-70"></div>
            <div className="absolute top-2 right-4 w-6 h-4 bg-green-400 rounded opacity-70"></div>
            <div className="absolute bottom-2 left-6 w-4 h-3 bg-red-400 rounded opacity-70"></div>
            <div className="absolute bottom-2 right-6 w-4 h-3 bg-yellow-400 rounded opacity-70"></div>
          </div>
          
          {/* Speech bubbles */}
          <div className="flex justify-around w-full mt-4">
            <div className="w-6 h-4 bg-blue-100 rounded-lg"></div>
            <div className="w-8 h-4 bg-green-100 rounded-lg"></div>
            <div className="w-5 h-4 bg-orange-100 rounded-lg"></div>
          </div>
        </div>
      </div>
    </div>
  );
}