import { Button } from "./ui/button";
import { Card } from "./ui/card";

interface FeatureCardProps {
  title: string;
  subtitle: string;
  icon: string;
  primaryAction: string;
  secondaryAction: string;
  iconBg?: string;
}

export function FeatureCard({ 
  title, 
  subtitle, 
  icon, 
  primaryAction, 
  secondaryAction,
  iconBg = "bg-[#FF6B6B]"
}: FeatureCardProps) {
  return (
    <Card className="bg-white rounded-3xl shadow-lg border-0 overflow-hidden h-full">
      {/* Header */}
      <div className="bg-gradient-to-r from-[#FFE5CC] to-[#FFD4B8] p-4 flex items-center gap-3">
        <div className={`w-8 h-8 ${iconBg} rounded-lg flex items-center justify-center`}>
          <span className="text-white text-sm">{icon}</span>
        </div>
        <h3 className="text-gray-800 font-medium">{title}</h3>
      </div>
      
      {/* Content */}
      <div className="p-4 space-y-4">
        <p className="text-gray-600 text-sm">{subtitle}</p>
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="flex-1 rounded-full border-[#FF6B6B] text-[#FF6B6B] hover:bg-[#FF6B6B] hover:text-white text-xs"
          >
            {primaryAction}
          </Button>
          <Button 
            size="sm" 
            className="flex-1 rounded-full bg-[#FF6B6B] hover:bg-[#FF6B6B]/90 text-white text-xs"
          >
            {secondaryAction}
          </Button>
        </div>
      </div>
    </Card>
  );
}