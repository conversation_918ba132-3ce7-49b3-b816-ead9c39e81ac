import { Button } from "./ui/button";
import { Download, Users, Gift } from "lucide-react";

export function PremiumFooter() {
  return (
    <div className="p-4 space-y-3">
      <div className="grid grid-cols-2 gap-3">
        <Button className="bg-[#FF6B6B] hover:bg-[#FF6B6B]/90 text-white rounded-2xl h-14 flex items-center gap-2">
          <Download className="w-5 h-5" />
          <div className="text-left">
            <div className="text-xs opacity-90">For Premium</div>
            <div className="text-sm">Member Only</div>
          </div>
        </Button>
        
        <Button className="bg-[#FF6B6B] hover:bg-[#FF6B6B]/90 text-white rounded-2xl h-14 flex items-center gap-2">
          <Download className="w-5 h-5" />
          <div className="text-left">
            <div className="text-xs opacity-90">For Premium</div>
            <div className="text-sm">Members Only</div>
          </div>
        </Button>
      </div>
      
      <Button className="w-full bg-[#FFA07A] hover:bg-[#FFA07A]/90 text-white rounded-2xl h-14 flex items-center gap-3">
        <Gift className="w-5 h-5" />
        <span>Refer & Earn Market</span>
      </Button>
    </div>
  );
}