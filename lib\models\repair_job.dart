enum JobStatus { pending, inProgress, completed }

class RepairJob {
  final String deviceName;
  final String imei;
  final String problem;
  final JobStatus status;
  final String? customer;
  final String? assignedTo;

  const RepairJob({
    required this.deviceName,
    required this.imei,
    required this.problem,
    required this.status,
    this.customer,
    this.assignedTo,
  });

  String get statusText {
    switch (status) {
      case JobStatus.pending:
        return 'Pending';
      case JobStatus.inProgress:
        return 'In Progress';
      case JobStatus.completed:
        return 'Completed';
    }
  }

  Color get statusColor {
    switch (status) {
      case JobStatus.pending:
        return const Color(0xFFFBBF24);
      case JobStatus.inProgress:
        return const Color(0xFFF97316);
      case JobStatus.completed:
        return const Color(0xFF00FF88);
    }
  }
}
