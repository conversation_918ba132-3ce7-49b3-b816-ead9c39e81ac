import { Card } from "./ui/card";

interface StatsCardProps {
  title: string;
  value: string;
  total?: string;
  progress: number;
  color: string;
  icon: React.ReactNode;
}

export function StatsCard({ title, value, total, progress, color, icon }: StatsCardProps) {
  const circumference = 2 * Math.PI * 45;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  return (
    <Card className="bg-slate-800/50 border-slate-700 p-4 h-32">
      <div className="flex items-center justify-between h-full">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <div className="text-slate-400">{icon}</div>
            <h3 className="text-slate-300 text-sm">{title}</h3>
          </div>
          <div className="text-2xl text-white">{value}</div>
          {total && <div className="text-slate-400 text-xs">/{total}</div>}
        </div>
        
        <div className="relative w-16 h-16">
          <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 100 100">
            <circle
              cx="50"
              cy="50"
              r="45"
              stroke="currentColor"
              strokeWidth="8"
              fill="transparent"
              className="text-slate-700"
            />
            <circle
              cx="50"
              cy="50"
              r="45"
              stroke={color}
              strokeWidth="8"
              fill="transparent"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              strokeLinecap="round"
              className="transition-all duration-1000 ease-in-out"
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-xs" style={{ color }}>{progress}%</span>
          </div>
        </div>
      </div>
    </Card>
  );
}