import { Card } from "./ui/card";
import { Badge } from "./ui/badge";
import { Smartphone, CircuitBoard } from "lucide-react";

interface JobCardProps {
  deviceName: string;
  imei: string;
  problem: string;
  status: "pending" | "in-progress" | "completed";
  customer?: string;
  assignedTo?: string;
}

export function JobCard({ deviceName, imei, problem, status, customer, assignedTo }: JobCardProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case "pending":
        return {
          color: "bg-yellow-500/20 text-yellow-400 border-yellow-500/30",
          text: "Pending"
        };
      case "in-progress":
        return {
          color: "bg-orange-500/20 text-orange-400 border-orange-500/30",
          text: "In Progress"
        };
      case "completed":
        return {
          color: "bg-green-500/20 text-green-400 border-green-500/30",
          text: "Completed"
        };
      default:
        return {
          color: "bg-slate-500/20 text-slate-400 border-slate-500/30",
          text: "Unknown"
        };
    }
  };

  const statusConfig = getStatusConfig(status);

  return (
    <Card className="bg-slate-800/30 border-slate-700 p-4 hover:bg-slate-800/50 transition-colors">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-slate-700 rounded-lg flex items-center justify-center">
            <Smartphone className="w-5 h-5 text-slate-400" />
          </div>
          <div>
            <h3 className="text-white">{deviceName}</h3>
            <p className="text-slate-400 text-sm">IMEI: {imei}</p>
            {customer && <p className="text-slate-500 text-xs">{customer}</p>}
          </div>
        </div>
        <Badge className={statusConfig.color} variant="outline">
          {statusConfig.text}
        </Badge>
      </div>
      
      <div className="mb-3">
        <div className="flex items-center gap-2 mb-1">
          <CircuitBoard className="w-4 h-4 text-slate-400" />
          <span className="text-slate-300 text-sm">Problem</span>
        </div>
        <p className="text-white text-sm pl-6">{problem}</p>
      </div>
      
      {assignedTo && (
        <div className="flex items-center justify-between text-xs text-slate-400">
          <span>Assigned to: {assignedTo}</span>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-[#00ff88] rounded-full animate-pulse"></div>
            <span>Active</span>
          </div>
        </div>
      )}
    </Card>
  );
}