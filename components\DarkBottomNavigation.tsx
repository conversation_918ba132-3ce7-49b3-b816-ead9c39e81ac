import { Home, ClipboardList, Package, User } from "lucide-react";
import { useState } from "react";

interface NavItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
}

const navItems: NavItem[] = [
  { id: "home", label: "Home", icon: Home },
  { id: "jobs", label: "Job Card", icon: ClipboardList },
  { id: "inventory", label: "Inventory", icon: Package },
  { id: "profile", label: "Profile", icon: User },
];

export function DarkBottomNavigation() {
  const [activeTab, setActiveTab] = useState("home");

  return (
    <div className="bg-slate-900/95 backdrop-blur-sm border-t border-slate-700 rounded-t-[24px] relative">
      {/* Active indicator */}
      <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-[#00ff88] to-transparent"></div>
      
      <div className="flex py-4 px-2">
        {navItems.map((item) => {
          const IconComponent = item.icon;
          const isActive = activeTab === item.id;
          
          return (
            <button
              key={item.id}
              onClick={() => setActiveTab(item.id)}
              className={`flex-1 py-2 px-4 flex flex-col items-center gap-1 transition-all duration-200 rounded-xl ${
                isActive 
                  ? "text-[#00ff88] bg-[#00ff88]/10" 
                  : "text-slate-400 hover:text-slate-300 hover:bg-slate-800/50"
              }`}
            >
              <IconComponent className={`w-6 h-6 ${isActive ? 'animate-pulse' : ''}`} />
              <span className="text-xs">{item.label}</span>
              {isActive && (
                <div className="w-1 h-1 bg-[#00ff88] rounded-full mt-1"></div>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
}