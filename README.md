# RepairPro Manager - Flutter App

एक comprehensive mobile repair shop management application जो Flutter में बनाई गई है।

## Features

### 📊 Dashboard
- **Daily Overview**: आज के jobs, low stock parts, pending payments, और technicians की real-time statistics
- **Circular Progress Indicators**: हर stat के लिए visual progress indicators
- **Color-coded Status**: अलग-अलग categories के लिए अलग colors

### 🔧 Repair Jobs Management
- **Job Cards**: हर repair job के लिए detailed cards
- **Status Tracking**: Pending, In Progress, और Completed status
- **Device Information**: Device name, IMEI, customer details
- **Technician Assignment**: कौन सा technician किस job पर काम कर रहा है
- **Problem Description**: क्या problem है device में

### 🎨 Modern UI/UX
- **Dark Theme**: Professional dark gradient background
- **Glassmorphism Effects**: Modern glass-like card designs
- **Smooth Animations**: Loading animations और transitions
- **Responsive Design**: सभी screen sizes के लिए optimized

### 🚀 Quick Actions
- **New Repair**: नया repair job add करने के लिए
- **Scan IMEI**: IMEI scanner functionality
- **Inventory**: Parts inventory management

### 📱 Navigation
- **Bottom Navigation**: 5 main sections:
  - Dashboard
  - Jobs
  - Inventory
  - Customers
  - Reports

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── models/
│   ├── daily_stat.dart      # Daily statistics model
│   └── repair_job.dart      # Repair job model
├── screens/
│   └── home_screen.dart     # Main dashboard screen
└── widgets/
    ├── dark_header.dart     # App header component
    ├── stats_card.dart      # Statistics card widget
    ├── job_card.dart        # Repair job card widget
    ├── bottom_navigation.dart # Bottom navigation bar
    └── loading_animation.dart # Loading animations
```

## Installation

1. **Flutter Setup**: Make sure Flutter is installed on your system
   ```bash
   flutter doctor
   ```

2. **Dependencies Install**: 
   ```bash
   flutter pub get
   ```

3. **Run the App**:
   ```bash
   flutter run
   ```

## Dependencies

- `flutter`: Core Flutter framework
- `google_fonts`: Custom fonts (Inter font family)
- `flutter_svg`: SVG support for icons
- `cupertino_icons`: iOS style icons

## Color Scheme

- **Primary Green**: `#00FF88` - Success states, active elements
- **Warning Yellow**: `#FBBF24` - Warning states
- **Error Orange**: `#F97316` - Error/urgent states  
- **Error Red**: `#EF4444` - Critical errors
- **Background Dark**: `#0F172A` - Main background
- **Surface Dark**: `#1E293B` - Card backgrounds
- **Text Colors**: Various shades of gray for hierarchy

## Features to Add

- [ ] Add new repair job functionality
- [ ] IMEI scanner integration
- [ ] Inventory management screens
- [ ] Customer management
- [ ] Reports and analytics
- [ ] Push notifications
- [ ] Data persistence (SQLite/Hive)
- [ ] API integration
- [ ] Print receipts functionality

## Contributing

यह project को improve करने के लिए contributions welcome हैं!

## License

This project is licensed under the MIT License.
