import { Card } from "./ui/card";

export function StatsRow() {
  const stats = [
    { label: "Active Jobs", value: "12", color: "text-green-400" },
    { label: "Pending", value: "8", color: "text-yellow-400" },
    { label: "Completed", value: "45", color: "text-blue-400" },
    { label: "Revenue", value: "$2,340", color: "text-green-400" },
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4">
      {stats.map((stat, index) => (
        <Card key={index} className="bg-slate-800/50 border-slate-700 p-4">
          <div className="text-center">
            <div className={`text-2xl font-bold ${stat.color}`}>
              {stat.value}
            </div>
            <div className="text-slate-300 text-sm mt-1">{stat.label}</div>
          </div>
        </Card>
      ))}
    </div>
  );
}