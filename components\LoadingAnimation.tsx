interface LoadingAnimationProps {
  type?: "pulse" | "spin" | "bounce";
  size?: "sm" | "md" | "lg";
}

export function LoadingAnimation({ type = "pulse", size = "md" }: LoadingAnimationProps) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8"
  };

  if (type === "pulse") {
    return (
      <div className="flex items-center gap-1">
        <div className={`${sizeClasses[size]} bg-[#00ff88] rounded-full animate-pulse`} style={{animationDelay: "0ms"}}></div>
        <div className={`${sizeClasses[size]} bg-[#00ff88] rounded-full animate-pulse`} style={{animationDelay: "150ms"}}></div>
        <div className={`${sizeClasses[size]} bg-[#00ff88] rounded-full animate-pulse`} style={{animationDelay: "300ms"}}></div>
      </div>
    );
  }

  if (type === "spin") {
    return (
      <div className={`${sizeClasses[size]} border-2 border-slate-600 border-t-[#00ff88] rounded-full animate-spin`}></div>
    );
  }

  if (type === "bounce") {
    return (
      <div className="flex items-end gap-1">
        <div className={`${sizeClasses[size]} bg-[#00ff88] rounded-sm animate-bounce`} style={{animationDelay: "0ms"}}></div>
        <div className={`${sizeClasses[size]} bg-[#00ff88] rounded-sm animate-bounce`} style={{animationDelay: "100ms"}}></div>
        <div className={`${sizeClasses[size]} bg-[#00ff88] rounded-sm animate-bounce`} style={{animationDelay: "200ms"}}></div>
      </div>
    );
  }

  return null;
}