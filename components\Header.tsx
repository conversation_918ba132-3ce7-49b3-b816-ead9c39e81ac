import { Search, Check } from "lucide-react";
import { Badge } from "./ui/badge";
import exampleImage from 'figma:asset/ee8d87d5e68e69c64eaf78ff58c66da797ed65f5.png';

export function Header() {
  return (
    <div className="relative bg-gradient-to-br from-[#FFE5CC] to-[#FFD4B8] p-6 pb-8 overflow-hidden">
      {/* World Map Background */}
      <div 
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `url(${exampleImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      />
      
      {/* Content */}
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-[#FF6B6B] rounded-full flex items-center justify-center">
              <span className="text-white text-lg">S</span>
            </div>
            <div>
              <h1 className="text-gray-800 text-xl">Sujan Mobile</h1>
              <p className="text-gray-600 text-sm">Mobile Udaipur</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Badge className="bg-[#FF6B6B] hover:bg-[#FF6B6B]/90 text-white border-0 px-3 py-1">
              <Check className="w-4 h-4 mr-1" />
              Admin
            </Badge>
            <button className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <Search className="w-4 h-4 text-gray-700" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}