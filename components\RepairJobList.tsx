import { Card } from "./ui/card";
import { Badge } from "./ui/badge";
import { Clock, User, Wrench } from "lucide-react";

interface RepairJob {
  id: string;
  customerName: string;
  deviceType: string;
  issue: string;
  status: "active" | "pending" | "completed";
  priority: "high" | "medium" | "low";
  assignedTo: string;
  estimatedTime: string;
  cost: string;
}

const mockJobs: RepairJob[] = [
  {
    id: "R001",
    customerName: "<PERSON> Smith",
    deviceType: "iPhone 13",
    issue: "Screen replacement",
    status: "active",
    priority: "high",
    assignedTo: "<PERSON>",
    estimatedTime: "2 hours",
    cost: "$150"
  },
  {
    id: "R002",
    customerName: "<PERSON>",
    deviceType: "Samsung Galaxy S21",
    issue: "Battery replacement",
    status: "pending",
    priority: "medium",
    assignedTo: "Not assigned",
    estimatedTime: "1 hour",
    cost: "$80"
  },
  {
    id: "R003",
    customerName: "<PERSON>",
    deviceType: "MacBook Pro",
    issue: "Keyboard repair",
    status: "active",
    priority: "low",
    assignedTo: "<PERSON> Chen",
    estimatedTime: "3 hours",
    cost: "$200"
  },
  {
    id: "R004",
    customerName: "Emily Brown",
    deviceType: "iPad Air",
    issue: "Screen crack",
    status: "completed",
    priority: "high",
    assignedTo: "<PERSON> <PERSON>",
    estimatedTime: "1.5 hours",
    cost: "$120"
  },
  {
    id: "R005",
    customerName: "David Miller",
    deviceType: "Google Pixel 6",
    issue: "Camera repair",
    status: "pending",
    priority: "medium",
    assignedTo: "Not assigned",
    estimatedTime: "2.5 hours",
    cost: "$90"
  }
];

export function RepairJobList() {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500/20 text-green-400 border-green-500/30";
      case "pending":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      case "completed":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-500/20 text-red-400 border-red-500/30";
      case "medium":
        return "bg-orange-500/20 text-orange-400 border-orange-500/30";
      case "low":
        return "bg-green-500/20 text-green-400 border-green-500/30";
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  return (
    <div className="flex-1 overflow-auto p-4 space-y-4">
      {mockJobs.map((job) => (
        <Card key={job.id} className="bg-slate-800/30 border-slate-700 p-4">
          <div className="flex items-start justify-between mb-3">
            <div>
              <h3 className="text-white text-lg">{job.customerName}</h3>
              <p className="text-slate-400">{job.deviceType}</p>
            </div>
            <div className="flex gap-2">
              <Badge className={getPriorityColor(job.priority)} variant="outline">
                {job.priority}
              </Badge>
              <Badge className={getStatusColor(job.status)} variant="outline">
                {job.status}
              </Badge>
            </div>
          </div>
          
          <div className="mb-3">
            <p className="text-white">{job.issue}</p>
            <p className="text-2xl text-[#00FFAA] mt-1">{job.cost}</p>
          </div>
          
          <div className="flex items-center justify-between text-sm text-slate-400">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                <User className="w-4 h-4" />
                <span>{job.assignedTo}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                <span>{job.estimatedTime}</span>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <Wrench className="w-4 h-4" />
              <span>#{job.id}</span>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}