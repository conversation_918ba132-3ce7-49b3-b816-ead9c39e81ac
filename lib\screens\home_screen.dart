import 'package:flutter/material.dart';
import '../models/daily_stat.dart';
import '../models/repair_job.dart';
import '../widgets/dark_header.dart';
import '../widgets/stats_card.dart';
import '../widgets/job_card.dart';
import '../widgets/bottom_navigation.dart';
import '../widgets/loading_animation.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final dailyStats = [
      const DailyStat(
        title: "Jobs Today",
        value: "5",
        total: "10",
        progress: 50,
        color: Color(0xFF00FF88),
        icon: Icons.build,
      ),
      const DailyStat(
        title: "Parts Low in Stock",
        value: "5",
        progress: 23,
        color: Color(0xFFFBBF24),
        icon: Icons.inventory,
      ),
      const DailyStat(
        title: "Pending Payments",
        value: "1",
        progress: 80,
        color: Color(0xFFF97316),
        icon: Icons.credit_card,
      ),
      const DailyStat(
        title: "Technicians On Duty",
        value: "0",
        progress: 0,
        color: Color(0xFFEF4444),
        icon: Icons.people,
      ),
    ];

    final repairJobs = [
      const RepairJob(
        deviceName: "SS S20/1",
        imei: "SS BB (E)",
        problem: "Screen replacement",
        status: JobStatus.pending,
        customer: "John Doe",
        assignedTo: "Mike",
      ),
      const RepairJob(
        deviceName: "701 E29/20",
        imei: "SS R/00",
        problem: "Battery replacement",
        status: JobStatus.inProgress,
        customer: "Sarah Wilson",
        assignedTo: "Alex",
      ),
      const RepairJob(
        deviceName: "SS4 308 390",
        imei: "SS R2 00",
        problem: "Camera repair",
        status: JobStatus.inProgress,
        customer: "Robert Brown",
        assignedTo: "Lisa",
      ),
      const RepairJob(
        deviceName: "S HBB03 D118",
        imei: "SS5114O",
        problem: "Water damage repair",
        status: JobStatus.inProgress,
        customer: "Emily Davis",
        assignedTo: "Tom",
      ),
      const RepairJob(
        deviceName: "1GB 794300",
        imei: "Device info",
        problem: "Charging port replacement",
        status: JobStatus.completed,
        customer: "David Miller",
        assignedTo: "Mike",
      ),
    ];

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF1A1F2E),
              Color(0xFF0F172A),
            ],
          ),
        ),
        child: Column(
          children: [
            const DarkHeader(),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.only(bottom: 100),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Daily Statistics Section
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Row(
                            children: [
                              Icon(
                                Icons.analytics,
                                color: Color(0xFF00FF88),
                                size: 20,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'Daily Overview',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          GridView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 2,
                              crossAxisSpacing: 16,
                              mainAxisSpacing: 16,
                              childAspectRatio: 1.5,
                            ),
                            itemCount: dailyStats.length,
                            itemBuilder: (context, index) {
                              return StatsCard(stat: dailyStats[index]);
                            },
                          ),
                        ],
                      ),
                    ),

                    // Repair Jobs Section
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Row(
                                children: [
                                  Icon(
                                    Icons.smartphone,
                                    color: Color(0xFF00FF88),
                                    size: 20,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    'Repair Jobs',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                              const LoadingAnimation(
                                type: LoadingType.pulse,
                                size: 16,
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ListView.separated(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: repairJobs.length,
                            separatorBuilder: (context, index) => const SizedBox(height: 12),
                            itemBuilder: (context, index) {
                              return JobCard(job: repairJobs[index]);
                            },
                          ),
                          const SizedBox(height: 24),

                          // Load More Button
                          Center(
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                              decoration: BoxDecoration(
                                color: const Color(0xFF00FF88).withOpacity(0.2),
                                borderRadius: BorderRadius.circular(24),
                                border: Border.all(
                                  color: const Color(0xFF00FF88).withOpacity(0.3),
                                  width: 1,
                                ),
                              ),
                              child: const Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  LoadingAnimation(
                                    type: LoadingType.spin,
                                    size: 16,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    'Load More',
                                    style: TextStyle(
                                      color: Color(0xFF00FF88),
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Quick Actions Section
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Quick Actions',
                            style: TextStyle(
                              color: Color(0xFFCBD5E1),
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 12),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: [
                                _buildQuickActionButton(
                                  icon: Icons.build,
                                  label: 'New Repair',
                                ),
                                const SizedBox(width: 12),
                                _buildQuickActionButton(
                                  icon: Icons.qr_code_scanner,
                                  label: 'Scan IMEI',
                                ),
                                const SizedBox(width: 12),
                                _buildQuickActionButton(
                                  icon: Icons.inventory,
                                  label: 'Inventory',
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: const DarkBottomNavigation(),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
  }) {
    return Container(
      width: 120,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B).withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF475569),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: const Color(0xFF00FF88),
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              color: Color(0xFFCBD5E1),
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
